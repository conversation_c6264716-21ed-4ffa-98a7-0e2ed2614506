import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Bot, MessageCircle } from 'lucide-react'
import { type Chat } from '@/types/types'
import { cn } from '@/lib/utils'

interface ChatCardProps {
  chat: Chat
  onClick: () => void
  className?: string
}

export function ChatCard({ chat, onClick, className }: ChatCardProps) {
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return ''

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date
      if (isNaN(dateObj.getTime())) return ''

      return dateObj.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })
    } catch {
      return ''
    }
  }

  return (
    <Card
      className={cn(
        'hover:shadow-md transition-all cursor-pointer hover:scale-[1.02]',
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start gap-3">
          <Avatar className="h-12 w-12">
            <AvatarFallback>
              <Bot className="h-6 w-6" />
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg truncate">{chat.name}</CardTitle>
            <CardDescription className="mt-1 line-clamp-2">
              {chat.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <MessageCircle className="h-4 w-4" />
          <span>Chat criado em {formatDate(chat?.createdAt)}</span>
        </div>

        <div className="text-xs text-muted-foreground">
          Criado por: {chat.createdBy}
        </div>
      </CardContent>
    </Card>
  )
}
