import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Plus } from 'lucide-react'
import { type CreateBotModalProps, type CreateBotRequest } from '@/types/types'
import { useAuth } from '@/hooks/useAuth'

export const CreateBotModal = ({ onCreateBot, isLoading = false }: CreateBotModalProps) => {
  const { user } = useAuth();
  const [open, setOpen] = useState(false)
  const [formData, setFormData] = useState<CreateBotRequest>({
    name: '',
    description: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim() || !formData.description.trim()) {
      return
    }

    try {
      if (!user) return
      await onCreateBot(formData.name.trim(), formData.description.trim(), user?.id)

      setFormData({ name: '', description: '' })
      setOpen(false)
    } catch (error) {
      console.error('Error creating bot:', error)
    }
  }

  const handleInputChange = (field: keyof CreateBotRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Criar Novo Bot
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Criar Novo Bot</DialogTitle>
          <DialogDescription>
            Crie um novo bot personalizado para suas conversas.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Nome do Bot *</Label>
              <Input
                id="name"
                placeholder="Ex: Assistente de Vendas"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                disabled={isLoading}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Descrição *</Label>
              <Input
                id="description"
                placeholder="Ex: Bot especializado em atendimento ao cliente"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                disabled={isLoading}
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !formData.name.trim() || !formData.description.trim()}
            >
              {isLoading ? 'Criando...' : 'Criar Bot'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
