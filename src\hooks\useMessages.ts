import { useState, useEffect, useCallback } from 'react'
import { api } from '@/services/api'
import { type Message, type PaginatedResponse } from '@/types/types'
import { toast } from 'sonner'

export const useMessages = (chatId?: string) => {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)

  const fetchMessages = useCallback(async (pageNum: number = 1, reset: boolean = false) => {
    if (!chatId) return

    try {
      setIsLoading(true)
      setError(null)

      const params = new URLSearchParams()
      params.append('chatId', chatId)
      params.append('page', pageNum.toString())
      params.append('limit', '20')

      const response = await api.get<PaginatedResponse<Message>>('/message', { params })
      const { data, totalPages } = response.data

      if (reset) {
        setMessages(data.reverse()) // Reverter para mostrar mensagens mais recentes no final
      } else {
        setMessages(prev => [...data.reverse(), ...prev]) // Adicionar mensagens mais antigas no início
      }

      setHasMore(pageNum < totalPages)
      setPage(pageNum)
    } catch (err) {
      const errorMessage = 'Erro ao carregar mensagens'
      setError(errorMessage)
      toast.error(errorMessage)
      console.error('Error fetching messages:', err)
    } finally {
      setIsLoading(false)
    }
  }, [chatId])

  const loadMoreMessages = useCallback(async () => {
    if (!hasMore || isLoading) return
    await fetchMessages(page + 1, false)
  }, [hasMore, isLoading, page, fetchMessages])

  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!chatId || !content.trim()) return false

    try {
      setIsSending(true)
      setError(null)

      // Adicionar mensagem do usuário imediatamente
      const tempUserMessage: Message = {
        id: `temp-${Date.now()}`,
        chatId,
        content: content.trim(),
        isUserMessage: true,
        createdAt: new Date(),
      }

      setMessages(prev => [...prev, tempUserMessage])

      // Adicionar mensagem de loading do bot
      const tempBotMessage: Message = {
        id: `loading-${Date.now()}`,
        chatId,
        content: 'Digitando...',
        isUserMessage: false,
        createdAt: new Date(),
      }

      setMessages(prev => [...prev, tempBotMessage])

      const response = await api.post<{ data: { userMessage: Message; botMessage: Message } }>('/message', {
        chatId,
        content: content.trim(),
      })

      const { userMessage, botMessage } = response.data.data

      // Substituir mensagens temporárias pelas finais
      setMessages(prev => 
        prev
          .filter(msg => !msg.id.startsWith('temp-') && !msg.id.startsWith('loading-'))
          .concat([userMessage, botMessage])
      )

      return true
    } catch (err) {
      // Remover mensagens temporárias em caso de erro
      setMessages(prev => 
        prev.filter(msg => !msg.id.startsWith('temp-') && !msg.id.startsWith('loading-'))
      )

      const errorMessage = 'Erro ao enviar mensagem'
      setError(errorMessage)
      toast.error(errorMessage)
      console.error('Error sending message:', err)
      return false
    } finally {
      setIsSending(false)
    }
  }, [chatId])

  const clearMessages = useCallback(() => {
    setMessages([])
    setPage(1)
    setHasMore(true)
    setError(null)
  }, [])

  useEffect(() => {
    if (chatId) {
      clearMessages()
      fetchMessages(1, true)
    }
  }, [chatId, fetchMessages, clearMessages])

  return {
    messages,
    isLoading,
    isSending,
    error,
    hasMore,
    page,
    fetchMessages,
    loadMoreMessages,
    sendMessage,
    clearMessages,
  }
}
